import 'dart:convert';
import 'package:flutter/foundation.dart' show debugPrint;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:get_it/get_it.dart';
import 'package:dartz/dartz.dart';
import 'package:hive_flutter/hive_flutter.dart';

import '../../core/errors/failures.dart';
import '../../core/storage/hive_config.dart';
import '../../domain/entities/cart_item.dart';
import '../../domain/entities/product.dart';
import '../../domain/usecases/get_cart_items_usecase.dart';
import '../../domain/usecases/add_to_cart_usecase.dart';
import '../../domain/usecases/remove_from_cart_usecase.dart';
import '../../domain/usecases/update_cart_quantity_usecase.dart';
import '../../domain/usecases/clear_cart_usecase.dart';
import '../../domain/usecases/get_cart_total_price_usecase.dart';
import '../../domain/usecases/get_cart_item_count_usecase.dart';
import '../../domain/usecases/is_in_cart_usecase.dart';

// Get the service locator instance
final sl = GetIt.instance;

// Debug function to check if cart dependencies are registered
// This is called when the cart provider is initialized
bool _checkCartDependencies() {
  try {
    final isRegistered = sl.isRegistered<GetCartItemsUseCase>();
    debugPrint('GetCartItemsUseCase registered: $isRegistered');
    return isRegistered;
  } catch (e) {
    debugPrint('Error checking cart dependencies: $e');
    return false;
  }
}

// Call the check function immediately to verify dependencies
final bool _cartDependenciesRegistered = _checkCartDependencies();

/// Cart state class to manage cart-related state
class CartState {
  final bool isLoading;
  final String? errorMessage;
  final List<CartItem> items;
  final double totalPrice;
  final int itemCount;

  /// Total quantity of items (sum of all quantities)
  int get totalQuantity => items.fold(0, (total, item) => total + item.quantity);

  CartState({
    this.isLoading = false,
    this.errorMessage,
    this.items = const [],
    this.totalPrice = 0.0,
    this.itemCount = 0,
  });

  CartState copyWith({
    bool? isLoading,
    String? errorMessage,
    List<CartItem>? items,
    double? totalPrice,
    int? itemCount,
  }) {
    return CartState(
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
      items: items ?? this.items,
      totalPrice: totalPrice ?? this.totalPrice,
      itemCount: itemCount ?? this.itemCount,
    );
  }
}

/// Cart notifier for handling cart operations
class CartNotifier extends StateNotifier<CartState> {
  final GetCartItemsUseCase getCartItemsUseCase;
  final AddToCartUseCase addToCartUseCase;
  final RemoveFromCartUseCase removeFromCartUseCase;
  final UpdateCartQuantityUseCase updateCartQuantityUseCase;
  final ClearCartUseCase clearCartUseCase;
  final GetCartTotalPriceUseCase getCartTotalPriceUseCase;
  final GetCartItemCountUseCase getCartItemCountUseCase;
  final IsInCartUseCase isInCartUseCase;

  CartNotifier({
    required this.getCartItemsUseCase,
    required this.addToCartUseCase,
    required this.removeFromCartUseCase,
    required this.updateCartQuantityUseCase,
    required this.clearCartUseCase,
    required this.getCartTotalPriceUseCase,
    required this.getCartItemCountUseCase,
    required this.isInCartUseCase,
  }) : super(CartState()) {
    // Initialize cart count immediately to ensure UI shows correct count
    _initializeCartCount();
    debugPrint('🛒 CART NOTIFIER: Initialized with cart count initialization');
  }

  /// Initialize cart count without loading full cart data
  Future<void> _initializeCartCount() async {
    try {
      final result = await getCartItemCountUseCase();
      result.fold(
        (failure) {
          debugPrint('🛒 CART COUNT INIT: Failed to get cart count - ${failure.message}');
        },
        (count) {
          debugPrint('🛒 CART COUNT INIT: Loaded cart count = $count');
          state = state.copyWith(itemCount: count);
        },
      );
    } catch (e) {
      debugPrint('🛒 CART COUNT INIT: Error getting cart count - $e');
    }
  }

  /// Refresh cart count from database
  Future<void> _refreshCartCount() async {
    try {
      final result = await getCartItemCountUseCase();
      result.fold(
        (failure) {
          debugPrint('🛒 CART COUNT REFRESH: Failed to refresh cart count - ${failure.message}');
        },
        (count) {
          debugPrint('🛒 CART COUNT REFRESH: Refreshed cart count = $count');
          state = state.copyWith(itemCount: count);
        },
      );
    } catch (e) {
      debugPrint('🛒 CART COUNT REFRESH: Error refreshing cart count - $e');
    }
  }

  /// Update the new cart count system
  void _updateNewCartCount(int quantity) {
    try {
      // Update the global new cart count system
      updateGlobalCartCount();
      debugPrint('🛒 CART NOTIFIER: Triggered new cart count update with quantity: $quantity');
    } catch (e) {
      debugPrint('🛒 CART NOTIFIER: Error updating new cart count - $e');
    }
  }

  /// Internal method to load initial cart data
  Future<void> _loadCartData() async {
    debugPrint('🛒 CART NOTIFIER: Loading initial cart data');
    state = state.copyWith(isLoading: true, errorMessage: null);

    await _refreshCartData();
    debugPrint('🛒 CART NOTIFIER: Initial cart data loaded, count = ${state.itemCount}');
  }

  /// Refreshes all cart data (items, total price, count)
  Future<void> _refreshCartData() async {
    final itemsResult = await getCartItemsUseCase();

    itemsResult.fold(
      (failure) => state = state.copyWith(
        isLoading: false,
        errorMessage: _mapFailureToMessage(failure),
      ),
      (items) async {
        // Get total price
        final totalPriceResult = await getCartTotalPriceUseCase();
        // Get item count
        final itemCountResult = await getCartItemCountUseCase();

        double totalPrice = 0;
        int itemCount = 0;

        totalPriceResult.fold(
          (failure) => null,
          (price) => totalPrice = price,
        );

        itemCountResult.fold(
          (failure) => null,
          (count) => itemCount = count,
        );

        state = state.copyWith(
          isLoading: false,
          items: items,
          totalPrice: totalPrice,
          itemCount: itemCount,
        );
      },
    );
  }

  /// Get all items in the cart
  Future<void> getCartItems() async {
    state = state.copyWith(isLoading: true, errorMessage: null);
    await _refreshCartData();
  }

  /// Add a product to the cart with optimistic updates
  Future<bool> addToCart({required Product product, required int quantity}) async {
    // Store original state for rollback
    final originalItems = List<CartItem>.from(state.items);
    final originalTotalPrice = state.totalPrice;
    final originalItemCount = state.itemCount;

    // Check if product already exists in cart
    final existingItemIndex = state.items.indexWhere((item) => item.product.id == product.id);

    List<CartItem> updatedItems;

    if (existingItemIndex != -1) {
      // Update existing item quantity
      updatedItems = List<CartItem>.from(state.items);
      final existingItem = updatedItems[existingItemIndex];
      updatedItems[existingItemIndex] = existingItem.copyWith(
        quantity: existingItem.quantity + quantity,
      );
    } else {
      // Add new item (create temporary cart item for optimistic update)
      updatedItems = List<CartItem>.from(state.items);
      final tempCartItem = CartItem(
        id: 'temp_${DateTime.now().millisecondsSinceEpoch}', // Temporary ID
        product: product,
        quantity: quantity,
        addedAt: DateTime.now(),
      );
      updatedItems.add(tempCartItem);
    }

    // Calculate new totals optimistically
    final newTotalPrice = updatedItems.fold<double>(
      0.0,
      (total, item) => total + (item.product.price * item.quantity),
    );
    final newItemCount = updatedItems.fold<int>(
      0,
      (total, item) => total + item.quantity,
    );

    // Update state immediately (optimistic)
    state = state.copyWith(
      items: updatedItems,
      totalPrice: newTotalPrice,
      itemCount: newItemCount,
      errorMessage: null,
    );

    debugPrint('🛒 ADD TO CART: Updated cart count to $newItemCount (optimistic)');

    // Also refresh cart count from database to ensure consistency (async)
    _refreshCartCount();

    // Update the new cart count system immediately
    _updateNewCartCount(quantity);

    // Perform actual add in background
    final result = await addToCartUseCase(
      AddToCartParams(product: product, quantity: quantity),
    );

    late bool success;

    result.fold(
      (failure) {
        // Rollback on failure
        state = state.copyWith(
          items: originalItems,
          totalPrice: originalTotalPrice,
          itemCount: originalItemCount,
          errorMessage: _mapFailureToMessage(failure),
        );
        success = false;
      },
      (cartItem) {
        // Success - replace temp item with real cart item if it was a new addition
        if (existingItemIndex == -1) {
          final finalItems = List<CartItem>.from(updatedItems);
          final tempItemIndex = finalItems.indexWhere((item) => item.id.startsWith('temp_'));
          if (tempItemIndex != -1) {
            finalItems[tempItemIndex] = cartItem;
            state = state.copyWith(items: finalItems);
          }
        }
        success = true;
      },
    );

    return success;
  }

  /// Remove an item from the cart with optimistic updates
  Future<bool> removeFromCart({required String cartItemId}) async {
    // Store original state for rollback
    final originalItems = List<CartItem>.from(state.items);
    final originalTotalPrice = state.totalPrice;
    final originalItemCount = state.itemCount;

    // Find the item to remove
    final itemIndex = state.items.indexWhere((item) => item.id == cartItemId);
    if (itemIndex == -1) {
      state = state.copyWith(errorMessage: 'Item not found in cart');
      return false;
    }

    // Optimistic update: Remove item immediately
    final updatedItems = List<CartItem>.from(state.items);
    updatedItems.removeAt(itemIndex);

    // Calculate new totals optimistically
    final newTotalPrice = updatedItems.fold<double>(
      0.0,
      (total, item) => total + (item.product.price * item.quantity),
    );
    final newItemCount = updatedItems.fold<int>(
      0,
      (total, item) => total + item.quantity,
    );

    // Update state immediately (optimistic)
    state = state.copyWith(
      items: updatedItems,
      totalPrice: newTotalPrice,
      itemCount: newItemCount,
      errorMessage: null,
    );

    // Update the new cart count system immediately
    updateGlobalCartCount();

    // Perform actual removal in background
    final result = await removeFromCartUseCase(
      RemoveFromCartParams(cartItemId: cartItemId),
    );

    late bool success;

    result.fold(
      (failure) {
        // Rollback on failure
        state = state.copyWith(
          items: originalItems,
          totalPrice: originalTotalPrice,
          itemCount: originalItemCount,
          errorMessage: _mapFailureToMessage(failure),
        );
        success = false;
      },
      (removed) {
        // Success - keep the optimistic update
        success = removed;
      },
    );

    return success;
  }

  /// Update the quantity of an item in the cart with optimistic updates
  Future<bool> updateQuantity({required String cartItemId, required int quantity}) async {
    // Store original state for rollback
    final originalItems = List<CartItem>.from(state.items);
    final originalTotalPrice = state.totalPrice;
    final originalItemCount = state.itemCount;

    // Find the item to update
    final itemIndex = state.items.indexWhere((item) => item.id == cartItemId);
    if (itemIndex == -1) {
      state = state.copyWith(errorMessage: 'Item not found in cart');
      return false;
    }

    final originalItem = state.items[itemIndex];

    // Optimistic update: Update UI immediately
    final updatedItems = List<CartItem>.from(state.items);
    updatedItems[itemIndex] = originalItem.copyWith(quantity: quantity);

    // Calculate new totals optimistically
    final newTotalPrice = updatedItems.fold<double>(
      0.0,
      (total, item) => total + (item.product.price * item.quantity),
    );
    final newItemCount = updatedItems.fold<int>(
      0,
      (total, item) => total + item.quantity,
    );

    // Update state immediately (optimistic)
    state = state.copyWith(
      items: updatedItems,
      totalPrice: newTotalPrice,
      itemCount: newItemCount,
      errorMessage: null,
    );

    // Update the new cart count system immediately
    updateGlobalCartCount();

    // Perform actual update in background
    final result = await updateCartQuantityUseCase(
      UpdateCartQuantityParams(cartItemId: cartItemId, quantity: quantity),
    );

    late bool success;

    result.fold(
      (failure) {
        // Rollback on failure
        state = state.copyWith(
          items: originalItems,
          totalPrice: originalTotalPrice,
          itemCount: originalItemCount,
          errorMessage: _mapFailureToMessage(failure),
        );
        success = false;
      },
      (cartItem) {
        // Success - keep the optimistic update
        success = true;
      },
    );

    return success;
  }

  /// Clear the cart
  Future<bool> clearCart() async {
    state = state.copyWith(isLoading: true, errorMessage: null);

    final result = await clearCartUseCase();

    late bool success;

    result.fold(
      (failure) {
        state = state.copyWith(
          isLoading: false,
          errorMessage: _mapFailureToMessage(failure),
        );
        success = false;
      },
      (cleared) {
        state = state.copyWith(
          isLoading: false,
          items: [],
          totalPrice: 0,
          itemCount: 0,
        );
        // Update the new cart count system immediately
        updateGlobalCartCount();
        success = cleared;
      },
    );

    return success;
  }

  /// Check if a product is in the cart
  Future<bool> isInCart({required String productId}) async {
    final result = await isInCartUseCase(
      IsInCartParams(productId: productId),
    );

    return result.fold(
      (failure) => false,
      (isInCart) => isInCart,
    );
  }

  /// Refresh cart data silently (without loading state)
  Future<void> refreshCart() async {
    // Don't show loading state for refresh - keep current UI responsive

    // Get fresh cart items
    final itemsResult = await getCartItemsUseCase();

    itemsResult.fold(
      (failure) => state = state.copyWith(
        errorMessage: _mapFailureToMessage(failure),
      ),
      (items) async {
        // Get total price
        final totalPriceResult = await getCartTotalPriceUseCase();
        // Get item count
        final itemCountResult = await getCartItemCountUseCase();

        double totalPrice = 0;
        int itemCount = 0;

        totalPriceResult.fold(
          (failure) => null,
          (price) => totalPrice = price,
        );

        itemCountResult.fold(
          (failure) => null,
          (count) => itemCount = count,
        );

        state = state.copyWith(
          items: items,
          totalPrice: totalPrice,
          itemCount: itemCount,
          errorMessage: null, // Clear any previous errors on successful refresh
        );
      },
    );
  }
}

/// Helper to map failures to user-friendly messages
String _mapFailureToMessage(Failure failure) {
  switch (failure.runtimeType) {
    case ServerFailure:
      return 'Server error occurred. Please try again later.';
    case NetworkFailure:
      return 'Network error. Please check your internet connection.';
    case CacheFailure:
      return 'Error retrieving local data. Please restart the app.';
    default:
      return failure.message;
  }
}

/// Cart providers

/// Main cart state provider
final cartNotifierProvider = StateNotifierProvider<CartNotifier, CartState>((ref) {
  // Check if cart dependencies are registered
  if (!_cartDependenciesRegistered) {
    debugPrint('WARNING: Cart dependencies not registered properly. Cart functionality may not work.');
  }

  try {
    return CartNotifier(
      getCartItemsUseCase: sl<GetCartItemsUseCase>(),
      addToCartUseCase: sl<AddToCartUseCase>(),
      removeFromCartUseCase: sl<RemoveFromCartUseCase>(),
      updateCartQuantityUseCase: sl<UpdateCartQuantityUseCase>(),
      clearCartUseCase: sl<ClearCartUseCase>(),
      getCartTotalPriceUseCase: sl<GetCartTotalPriceUseCase>(),
      getCartItemCountUseCase: sl<GetCartItemCountUseCase>(),
      isInCartUseCase: sl<IsInCartUseCase>(),
    );
  } catch (e) {
    debugPrint('Error creating CartNotifier: $e');
    // Return a dummy CartNotifier with empty state to prevent app crashes
    throw StateError('Failed to initialize cart: $e');
  }
});

/// Convenience providers for specific cart states

/// Cart items provider
final cartItemsProvider = Provider<List<CartItem>>((ref) {
  return ref.watch(cartNotifierProvider).items;
});

/// Cart loading state provider
final cartLoadingProvider = Provider<bool>((ref) {
  return ref.watch(cartNotifierProvider).isLoading;
});

/// Cart error message provider
final cartErrorProvider = Provider<String?>((ref) {
  return ref.watch(cartNotifierProvider).errorMessage;
});

/// Cart total price provider
final cartTotalPriceProvider = Provider<double>((ref) {
  return ref.watch(cartNotifierProvider).totalPrice;
});

/// NEW: Simple cart item count provider that works independently
final newCartItemCountProvider = StateNotifierProvider<NewCartCountNotifier, int>((ref) {
  return NewCartCountNotifier();
});

/// SIMPLER: Cart count provider that directly reads from cart state
final simpleCartCountProvider = Provider<int>((ref) {
  final cartState = ref.watch(cartNotifierProvider);
  final count = cartState.totalQuantity; // Use totalQuantity which sums all item quantities
  debugPrint('🛒 SIMPLE CART COUNT: Current count = $count (from cart state)');
  return count;
});

/// NEW: Simple cart count notifier that directly manages cart count
class NewCartCountNotifier extends StateNotifier<int> {
  NewCartCountNotifier() : super(0) {
    debugPrint('🛒 NEW CART COUNT: Notifier created! Initializing...');
    // Set global reference for easy access
    setGlobalCartCountNotifier(this);
    // Initialize cart count immediately
    _loadCartCount();
    debugPrint('🛒 NEW CART COUNT: Notifier initialization completed');
  }

  /// Load cart count from storage using cart summary (much faster!)
  Future<void> _loadCartCount() async {
    try {
      debugPrint('🛒 NEW CART COUNT: Starting to load cart count...');

      // Check if Hive is initialized
      if (!Hive.isBoxOpen('cart_box')) {
        debugPrint('🛒 NEW CART COUNT: Cart box is not open, initializing...');
        await HiveConfig.initialize();
      }

      // Get cart summary from Hive storage (much faster than parsing all items)
      final cartBox = HiveConfig.cartBox;
      final cartMetadata = cartBox.get('cart_metadata'); // This contains the summary
      debugPrint('🛒 NEW CART COUNT: Cart metadata type: ${cartMetadata.runtimeType}');

      if (cartMetadata != null) {
        // Parse the cart summary JSON
        final Map<String, dynamic> summary = json.decode(cartMetadata as String);
        final totalItems = summary['total_items'] as int? ?? 0;

        debugPrint('🛒 NEW CART COUNT: ✅ Loaded count from summary = $totalItems');
        state = totalItems;
      } else {
        // Fallback: parse cart items directly if no summary exists
        debugPrint('🛒 NEW CART COUNT: No summary found, parsing cart items...');
        final cartData = cartBox.get('cart_items');

        if (cartData != null) {
          final List<dynamic> jsonList = json.decode(cartData as String);
          int totalCount = 0;
          for (final item in jsonList) {
            if (item is Map<String, dynamic>) {
              final quantity = item['quantity'] as int? ?? 0;
              totalCount += quantity;
            }
          }
          debugPrint('🛒 NEW CART COUNT: ✅ Loaded count from items = $totalCount');
          state = totalCount;
        } else {
          debugPrint('🛒 NEW CART COUNT: ⚠️ No cart data found, setting count to 0');
          state = 0;
        }
      }
    } catch (e, stackTrace) {
      debugPrint('🛒 NEW CART COUNT: ❌ Error loading count - $e');
      debugPrint('🛒 NEW CART COUNT: Stack trace: $stackTrace');
      state = 0;
    }
  }

  /// Update cart count (call this when items are added/removed)
  Future<void> updateCartCount() async {
    debugPrint('🛒 NEW CART COUNT: updateCartCount() called');
    await _loadCartCount();
    debugPrint('🛒 NEW CART COUNT: updateCartCount() completed, new state = $state');
  }

  /// Add to cart count (optimistic update)
  void addToCount(int quantity) {
    debugPrint('🛒 NEW CART COUNT: Adding $quantity to count (current: $state)');
    state = state + quantity;
    // Also refresh from storage to ensure accuracy
    _loadCartCount();
  }

  /// Remove from cart count (optimistic update)
  void removeFromCount(int quantity) {
    debugPrint('🛒 NEW CART COUNT: Removing $quantity from count (current: $state)');
    state = (state - quantity).clamp(0, double.infinity).toInt();
    // Also refresh from storage to ensure accuracy
    _loadCartCount();
  }

  /// Set cart count directly
  void setCount(int count) {
    debugPrint('🛒 NEW CART COUNT: Setting count to $count');
    state = count;
  }

  /// Clear cart count
  void clearCount() {
    debugPrint('🛒 NEW CART COUNT: Clearing count');
    state = 0;
  }
}

/// LEGACY: Cart item count provider (keeping for backward compatibility)
final cartItemCountProvider = Provider<int>((ref) {
  final count = ref.watch(cartNotifierProvider).itemCount;
  debugPrint('🛒 LEGACY CART COUNT PROVIDER: Current count = $count');
  return count;
});

/// Global reference to the new cart count notifier for easy access
NewCartCountNotifier? _globalNewCartCountNotifier;

/// Set the global cart count notifier reference
void setGlobalCartCountNotifier(NewCartCountNotifier notifier) {
  _globalNewCartCountNotifier = notifier;
  debugPrint('🛒 GLOBAL: Cart count notifier reference set');
}

/// Update the new cart count system globally
void updateGlobalCartCount() {
  debugPrint('🛒 GLOBAL: updateGlobalCartCount called');
  if (_globalNewCartCountNotifier != null) {
    debugPrint('🛒 GLOBAL: Global notifier found, updating cart count...');
    _globalNewCartCountNotifier!.updateCartCount();
    debugPrint('🛒 GLOBAL: Cart count updated successfully');
  } else {
    debugPrint('🛒 GLOBAL: ❌ Cart count notifier not set! This is the problem.');
  }
}

/// Cart item existence checker provider
final isProductInCartProvider = FutureProvider.autoDispose.family<bool, String>((ref, productId) async {
  // First check the current cart items directly (faster)
  final cartItems = ref.watch(cartItemsProvider);
  for (var item in cartItems) {
    if (item.product.id == productId) {
      debugPrint('isProductInCartProvider: Found product $productId in cart items');
      return true;
    }
  }

  // If not found in current items, check with the repository
  final isInCart = await ref.read(cartNotifierProvider.notifier).isInCart(productId: productId);
  debugPrint('isProductInCartProvider: Repository check for $productId returned $isInCart');
  return isInCart;
});